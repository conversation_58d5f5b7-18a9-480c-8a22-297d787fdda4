import { sma } from '../indicators/sma'

export function evaluateSmaCross(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const fast = sma(closes, 9)
	const slow = sma(closes, 14)

	const i = closes.length - 1
	if (i < 1) return undefined

	const f = fast[i],
		s = slow[i]
	const pf = fast[i - 1],
		ps = slow[i - 1]

	if ([f, s, pf, ps].some(v => v == null)) return undefined

	// TypeScript type assertions after null check
	const fastCurrent = f as number
	const slowCurrent = s as number
	const fastPrevious = pf as number
	const slowPrevious = ps as number

	if (fastPrevious < slowPrevious && fastCurrent > slowCurrent) return 'BUY'
	if (fastPrevious > slowPrevious && fastCurrent < slowCurrent) return 'SELL'
	return 'HOLD'
}
