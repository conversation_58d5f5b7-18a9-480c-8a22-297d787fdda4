import { RSI, BollingerBands, SMA } from 'technicalindicators'

export function evaluateRsiBbSma(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const rsi = RSI.calculate({ period: 14, values: closes })
	const bb = BollingerBands.calculate({ period: 20, stdDev: 2, values: closes })
	const sma = SMA.calculate({ period: 20, values: closes })

	const i = closes.length - 1
	const ri = rsi.length - 1
	const bi = bb.length - 1
	const si = sma.length - 1

	if (ri < 0 || bi < 0 || si < 0) return undefined

	const price = closes[i]
	const lastRSI = rsi[ri]
	const lastBB = bb[bi]
	const lastSMA = sma[si]

	// Check if any values are undefined before proceeding
	if (price == null || lastRSI == null || lastBB == null || lastSMA == null) return undefined

	if (lastRSI < 30 && price < lastBB.lower && price > lastSMA) return 'BUY'
	if (lastRSI > 70 && price > lastBB.upper && price < lastSMA) return 'SELL'

	return 'HOLD'
}
