interface Candle {
	time: number
	open: number
	high: number
	low: number
	close: number
}

type Signal = 'BUY' | 'SELL' | 'HOLD' | undefined
type StrategyFn = (candles: Candle[]) => Signal

type StrategyName =
	| 'SMA_CROSS'
	| 'RSI_BB_SMA'
	| 'RSI_MACD_BB_STOCH'
	| 'MACD_KC_CCI'
	| 'MACD_BB_STOCH'
	| 'MACD_KC_STOCH'
	| 'MACD_KC'
	| 'MACD_STOCH'
	| 'BB_STOCH'
	| 'KC_STOCH'
	| 'KC_CCI'
	| 'STOCH_CCI'
	| 'CCI_STOCH'
	| 'STOCH_MACD'
