import { evaluateMacdKcCci } from '../strategies/macdKcCci'
import { evaluateRsiBbSma } from '../strategies/rsiBbSma'
import { evaluateRsiMacdBbStoch } from '../strategies/rsiMacdBbStoch'
import { evaluateSmaCross } from '../strategies/smaCross'

const strategyMap: Record<StrategyName, StrategyFn> = {
	SMA_CROSS: evaluateSma<PERSON>ross,
	RSI_BB_SMA: evaluateRsiBbSma,
	RSI_MACD_BB_STOCH: evaluateRsiMacdBbStoch,
	MACD_KC_CCI: evaluateMacdKcCci
}

export class SignalEngine {
	private readonly candles: Candle[] = []
	private readonly strategy: StrategyFn
	private lastSignal: Signal = undefined

	constructor(strategyName: StrategyName) {
		const fn = strategyMap[strategyName]
		if (!fn) throw new Error(`Unknown strategy: ${strategyName}`)
		this.strategy = fn
	}

	update(candle: Candle): Signal {
		this.candles.push(candle)
		this.lastSignal = this.strategy(this.candles)
		return this.lastSignal
	}

	getLastSignal(): Signal {
		return this.lastSignal
	}

	reset(): void {
		this.candles.length = 0
		this.lastSignal = undefined
	}
}
