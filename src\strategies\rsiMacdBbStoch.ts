import { RSI, MACD, BollingerBands, Stochastic } from 'technicalindicators'

export function evaluateRsiMacdBbStoch(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const rsi = RSI.calculate({ period: 14, values: closes })
	const bb = BollingerBands.calculate({ period: 20, stdDev: 2, values: closes })
	const macd = MACD.calculate({
		values: closes,
		fastPeriod: 12,
		slowPeriod: 26,
		signalPeriod: 9,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})
	const stoch = Stochastic.calculate({
		period: 14,
		signalPeriod: 3,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const ri = rsi.length - 1
	const bi = bb.length - 1
	const mi = macd.length - 1
	const si = stoch.length - 1

	if (ri < 0 || bi < 0 || mi < 1 || si < 1) return undefined

	const lastRSI = rsi[ri]
	const lastBB = bb[bi]
	const lastMACD = macd[mi]
	const lastStoch = stoch[si]

	const price = closes[i]

	// Check if any values are undefined before proceeding
	if (price == null || lastRSI == null || lastBB == null || lastMACD == null || lastStoch == null) return undefined
	if (lastMACD.MACD == null || lastMACD.signal == null || lastStoch.k == null) return undefined

	const bullish = lastRSI < 30 && price < lastBB.lower && lastMACD.MACD > lastMACD.signal && lastStoch.k < 20

	const bearish = lastRSI > 70 && price > lastBB.upper && lastMACD.MACD < lastMACD.signal && lastStoch.k > 80

	if (bullish) return 'BUY'
	if (bearish) return 'SELL'
	return 'HOLD'
}
