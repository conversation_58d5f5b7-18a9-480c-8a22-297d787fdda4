import { io, type Socket } from 'socket.io-client'
import { Region } from '../constants'
import { extractAuth } from '../utils/parsers'
import { logger } from '../utils/logger'
import { formatData } from '../utils/formatter'
import { Order } from './channels/order'
import { Candles } from './channels/candles'

export class PocketOption {
	private ws?: Socket
	private balance: number = 0
	private isConnected: boolean = false
	private connectionTimeout: number = 10000 // 10 seconds
	private heartbeatInterval: NodeJS.Timeout | null = null

	private orderPayload: OrderPayload | null = null
	private orderResult: OrderResult | null = null
	private chartSettings: ChartSettings | null = null

	constructor(private ssID: string, private demo: boolean = true) {}

	connect(): Promise<void> {
		return new Promise((resolve, reject) => {
			const endpoint = this.demo ? Region.DEMO_REGION : Region.getRegion()[0]
			const options = Region.SOCKET_OPTIONS
			const auth = extractAuth(this.ssID)

			if (!auth) {
				return reject(new Error('Invalid ssID, authentication failed.'))
			}

			const { session, uid } = auth

			// Set connection timeout
			const timeout = setTimeout(() => {
				if (this.ws) {
					this.ws.disconnect()
				}
				reject(new Error('Connection timeout'))
			}, this.connectionTimeout)

			this.ws = io(endpoint, options)

			this.ws.once('connect', () => {
				if (this.ws) {
					this.ws.emit('auth', {
						isDemo: this.demo ? 1 : 0,
						isFastHistory: true,
						platform: 2,
						session,
						uid
					})
				}
			})

			this.ws.once('successauth', () => {
				clearTimeout(timeout)
				this.isConnected = true
				logger.success(`Broker`, `Authenticated successfully`)

				// Set up balance update listener
				this.setupListeners()

				// Start heartbeat
				this.startHeartbeat()

				resolve()
			})

			this.ws.onAny((event: string, ...args: unknown[]) => {
				logger.debug(`Broker`, `Received event: ${event}`)
			})

			this.ws.on('disconnect', () => {
				this.disconnect()
				logger.warn(`Broker`, `Disconnected from server`)
			})

			this.ws.on('error', (err: Error) => {
				clearTimeout(timeout)
				this.isConnected = false
				logger.error(`Broker`, `Error`, err)
				reject(err)
			})

			this.ws.on('connect_error', (err: Error) => {
				clearTimeout(timeout)
				this.isConnected = false
				logger.error(`Broker`, `Connection error`, err)
				reject(err)
			})
		})
	}

	async emit(event: string, data: unknown): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.emit(event, data)
			resolve()
		})
	}

	async on(event: string, callback: (data: unknown[]) => void): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.on(event, (data: unknown[]) => {
				const parsedData = formatData(data) as unknown[]
				callback(parsedData)
			})
			resolve()
		})
	}

	async once(event: string, callback: (data: unknown[]) => void): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.once(event, (data: unknown[]) => {
				const parsedData = formatData(data) as unknown[]
				callback(parsedData)
				resolve()
			})
		})
	}

	async getBalance(): Promise<number> {
		if (!this.ws || !this.isConnected) {
			throw new Error('Not connected to server')
		}

		return new Promise((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			if (this.balance !== 0) {
				resolve(this.balance)
			} else {
				this.once('successupdateBalance', (data: unknown) => {
					const balanceData = data as BalanceData

					this.balance = balanceData.balance
					resolve(balanceData.balance)
				})
			}
		})
	}

	async placeOrder(payload: OrderPayload): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			const order = Order.call(payload)

			resolve()
		})
	}

	async getCandles(assetSymbol: string): Promise<unknown[]> {
		if (!this.ws || !this.isConnected) {
			throw new Error('Not connected to server')
		}

		return new Promise<unknown[]>(async (resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			Candles.init(this)

			// Wait for chart settings to be available
			const chartSettings = await this.waitForChartSettings()
			await Candles.call(assetSymbol, chartSettings)

			this.once('loadHistoryPeriodFast', (data: unknown[]) => {
				console.log(JSON.stringify(data))
				resolve(data as unknown[])
			})
		})
	}
	/**
	 * Check if the broker is connected
	 */
	getConnectionStatus(): boolean {
		return this.isConnected && this.ws?.connected === true
	}

	getChartSettings(): ChartSettings | null {
		return this.chartSettings
	}

	/**
	 * Wait for chart settings to be received from the server
	 * @param timeout Maximum time to wait in milliseconds (default: 1000ms)
	 * @returns Promise that resolves with chart settings
	 */
	private async waitForChartSettings(timeout: number = 1000): Promise<ChartSettings> {
		// If chart settings are already available, return them immediately
		if (this.chartSettings) {
			return this.chartSettings
		}

		return new Promise<ChartSettings>((resolve, reject) => {
			const timeoutId = setTimeout(() => {
				reject(new Error('Timeout waiting for chart settings'))
			}, timeout)

			// Set up a listener for chart settings updates
			const checkSettings = () => {
				if (this.chartSettings) {
					clearTimeout(timeoutId)
					resolve(this.chartSettings)
				} else {
					// Check again in 100ms
					setTimeout(checkSettings, 100)
				}
			}

			// Start checking
			checkSettings()
		})
	}

	/**
	 * Disconnect from the broker
	 */
	disconnect(): void {
		if (this.ws) {
			this.stopHeartbeat()
			this.ws.disconnect()
			this.isConnected = false
			logger.info('Broker', 'Disconnected from server')
		}
	}

	/**
	 * Manually update the balance (useful for testing or external balance updates)
	 */
	updateBalance(newBalance: number): void {
		const oldBalance = this.balance
		this.balance = newBalance
		logger.info('Broker', `Balance manually updated: ${oldBalance} -> ${this.balance}`)
	}

	/**
	 * Set up listener for balance updates
	 */
	private setupListeners(): void {
		if (!this.ws) return

		this.ws.on('updateCharts', this.handleChartSettings)
	}

	private handleChartSettings = (data: unknown[]): void => {
		const parsedData = formatData(data) as ChartsData

		if (Array.isArray(parsedData) && parsedData.length > 0) {
			if (parsedData[0] && typeof parsedData[0].settings === 'object') {
				this.chartSettings = parsedData[0].settings as ChartSettings
			} else if (parsedData[0] && typeof parsedData[0].settings === 'string') {
				this.chartSettings = JSON.parse(parsedData[0].settings) as ChartSettings
			}
		}
	}

	private startHeartbeat(): void {
		if (!this.ws) return

		// Send a ping every 20 seconds
		this.heartbeatInterval = setInterval(() => {
			if (this.ws) {
				this.ws.emit('ps')
			}
		}, 20000)
	}

	private stopHeartbeat(): void {
		if (!this.heartbeatInterval) return

		clearInterval(this.heartbeatInterval)
		this.heartbeatInterval = null
	}
}
