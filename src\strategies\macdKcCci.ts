import { KeltnerChannels, CCI, MACD } from 'technicalindicators'

export function evaluateMacdKcCci(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const kc = KeltnerChannels.calculate({
		maPeriod: 20,
		atrPeriod: 10,
		useSMA: false,
		multiplier: 2,
		high: highs,
		low: lows,
		close: closes
	})

	const cci = CCI.calculate({ period: 20, high: highs, low: lows, close: closes })
	const macd = MACD.calculate({
		values: closes,
		fastPeriod: 12,
		slowPeriod: 26,
		signalPeriod: 9,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})

	const i = closes.length - 1
	const ki = kc.length - 1
	const ci = cci.length - 1
	const mi = macd.length - 1

	if (ki < 0 || ci < 0 || mi < 1) return undefined

	const price = closes[i]
	const lastKC = kc[ki]
	const lastCCI = cci[ci]
	const lastMACD = macd[mi]

	// Check if any values are undefined before proceeding
	if (price == null || lastKC == null || lastCCI == null || lastMACD == null) return undefined
	if (lastMACD.MACD == null || lastMACD.signal == null) return undefined

	const bullish = price < lastKC.lower && lastCCI < -100 && lastMACD.MACD > lastMACD.signal

	const bearish = price > lastKC.upper && lastCCI > 100 && lastMACD.MACD < lastMACD.signal

	if (bullish) return 'BUY'
	if (bearish) return 'SELL'
	return 'HOLD'
}
